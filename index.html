<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>متجر النخبة الإلكتروني</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #2563eb;
            --secondary-color: #f59e0b;
            --accent-color: #10b981;
            --text-color: #1f2937;
            --bg-color: #ffffff;
            --card-bg: #f9fafb;
            --border-color: #e5e7eb;
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }

        [data-theme="dark"] {
            --text-color: #f9fafb;
            --bg-color: #111827;
            --card-bg: #1f2937;
            --border-color: #374151;
        }

        body {
            font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: var(--bg-color);
            color: var(--text-color);
            transition: all 0.3s ease;
            overflow-x: hidden;
            font-weight: 400;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header */
        header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 1rem 0;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: var(--shadow-lg);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .logo {
            font-size: 1.8rem;
            font-weight: 800;
            font-family: 'Cairo', sans-serif;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            letter-spacing: -0.5px;
        }

        .search-container {
            flex: 1;
            max-width: 400px;
            position: relative;
        }

        .search-box {
            width: 100%;
            padding: 0.75rem 1rem;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            font-family: 'Cairo', sans-serif;
            font-weight: 400;
            outline: none;
            background: rgba(255, 255, 255, 0.9);
            color: var(--text-color);
        }

        .header-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .theme-toggle, .cart-btn, .wishlist-btn, .user-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 0.75rem;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .theme-toggle:hover, .cart-btn:hover, .wishlist-btn:hover, .user-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        .user-btn.logged-in {
            border-radius: 25px;
            padding: 0.5rem 1rem;
        }

        .user-name {
            font-size: 0.9rem;
            font-weight: 600;
            font-family: 'Cairo', sans-serif;
        }

        .user-avatar {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            margin-left: 8px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            font-size: 1.2rem;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(255, 255, 255, 0.2);
        }

        /* Avatar Selection Styles */
        .avatar-selection {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 10px;
            margin: 15px 0;
            max-width: 300px;
        }

        .avatar-option {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            border: 3px solid transparent;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            background: linear-gradient(135deg, #f0f0f0, #e0e0e0);
            position: relative;
        }

        .avatar-option:hover {
            transform: scale(1.1);
            border-color: var(--primary-color);
        }

        .avatar-option.selected {
            border-color: var(--accent-color);
            box-shadow: 0 0 0 2px var(--accent-color);
            transform: scale(1.05);
        }

        .avatar-option::after {
            content: '✓';
            position: absolute;
            bottom: -5px;
            right: -5px;
            background: var(--accent-color);
            color: white;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .avatar-option.selected::after {
            opacity: 1;
        }

        .badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background: var(--accent-color);
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 0.75rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Navigation */
        .nav-categories {
            background: var(--card-bg);
            padding: 1rem 0;
            border-bottom: 1px solid var(--border-color);
        }

        .categories {
            display: flex;
            gap: 2rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .category-btn {
            background: none;
            border: none;
            color: var(--text-color);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
            font-family: 'Cairo', sans-serif;
            font-size: 0.95rem;
        }

        .category-btn:hover, .category-btn.active {
            background: var(--primary-color);
            color: white;
            transform: translateY(-2px);
        }

        /* Hero Section */
        .hero {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            padding: 4rem 0;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="1" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="1" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        }

        .hero-content {
            position: relative;
            z-index: 1;
        }

        .hero h1 {
            font-size: 3rem;
            font-weight: 800;
            font-family: 'Cairo', sans-serif;
            margin-bottom: 1rem;
            animation: fadeInUp 1s ease;
            letter-spacing: -1px;
        }

        .hero p {
            font-size: 1.2rem;
            font-weight: 400;
            font-family: 'Cairo', sans-serif;
            margin-bottom: 2rem;
            animation: fadeInUp 1s ease 0.2s both;
            line-height: 1.8;
        }

        .cta-btn {
            background: var(--accent-color);
            color: white;
            border: none;
            padding: 1rem 2rem;
            font-size: 1.1rem;
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            animation: fadeInUp 1s ease 0.4s both;
        }

        .cta-btn:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-lg);
        }

        /* Products Grid */
        .products-section {
            padding: 4rem 0;
        }

        .section-title {
            text-align: center;
            font-size: 2.5rem;
            font-weight: 700;
            font-family: 'Cairo', sans-serif;
            margin-bottom: 3rem;
            color: var(--text-color);
            letter-spacing: -0.5px;
        }

        .filters {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .filter-btn {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            color: var(--text-color);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: 'Cairo', sans-serif;
            font-weight: 500;
            font-size: 0.9rem;
        }

        .filter-btn:hover, .filter-btn.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .product-card {
            background: var(--card-bg);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
            position: relative;
        }

        .product-card:hover {
            transform: translateY(-10px);
            box-shadow: var(--shadow-lg);
        }

        .product-image {
            width: 100%;
            height: 250px;
            background: linear-gradient(45deg, #f0f0f0, #e0e0e0);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 4rem;
            color: #ccc;
            position: relative;
            overflow: hidden;
        }

        .product-badge {
            position: absolute;
            top: 10px;
            left: 10px;
            background: var(--accent-color);
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 10px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .wishlist-heart {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            color: #ccc;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .wishlist-heart:hover, .wishlist-heart.active {
            color: #e74c3c;
            transform: scale(1.1);
        }

        .product-info {
            padding: 1.5rem;
        }

        .product-title {
            font-size: 1.2rem;
            font-weight: 700;
            font-family: 'Cairo', sans-serif;
            margin-bottom: 0.5rem;
            color: var(--text-color);
            line-height: 1.4;
        }

        .product-description {
            color: #6b7280;
            margin-bottom: 1rem;
            font-size: 0.9rem;
            font-family: 'Cairo', sans-serif;
            font-weight: 400;
            line-height: 1.6;
        }

        .product-rating {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .stars {
            color: var(--secondary-color);
        }

        .rating-text {
            color: #6b7280;
            font-size: 0.9rem;
            font-family: 'Cairo', sans-serif;
            font-weight: 400;
        }

        .product-price {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .current-price {
            font-size: 1.5rem;
            font-weight: 800;
            font-family: 'Cairo', sans-serif;
            color: var(--primary-color);
        }

        .old-price {
            text-decoration: line-through;
            color: #6b7280;
            font-family: 'Cairo', sans-serif;
            font-weight: 500;
        }

        .discount {
            background: #ef4444;
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 10px;
            font-size: 0.8rem;
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
        }

        .product-actions {
            display: flex;
            gap: 0.5rem;
        }

        .add-to-cart {
            flex: 1;
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 0.75rem;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
            font-family: 'Cairo', sans-serif;
            font-size: 0.9rem;
        }

        .add-to-cart:hover {
            background: #1d4ed8;
            transform: translateY(-2px);
        }

        .quick-view {
            background: var(--secondary-color);
            color: white;
            border: none;
            padding: 0.75rem;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .quick-view:hover {
            background: #d97706;
            transform: translateY(-2px);
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        /* Additional Cairo Font Styles */
        h1, h2, h3, h4, h5, h6 {
            font-family: 'Cairo', sans-serif;
        }

        button, input, select, textarea {
            font-family: 'Cairo', sans-serif;
        }

        .badge {
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
        }

        /* Floating Chat Button */
        .floating-chat {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #0088cc, #229ED9);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 1000;
            box-shadow: 0 8px 25px rgba(0, 136, 204, 0.3);
            transition: all 0.3s ease;
            animation: pulse-chat 2s infinite;
        }

        .floating-chat:hover {
            transform: scale(1.1);
            box-shadow: 0 12px 35px rgba(0, 136, 204, 0.4);
        }

        .floating-chat i {
            color: white;
            font-size: 1.5rem;
        }

        .chat-tooltip {
            position: absolute;
            right: 70px;
            top: 50%;
            transform: translateY(-50%);
            background: var(--text-color);
            color: var(--bg-color);
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 0.9rem;
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            box-shadow: var(--shadow);
        }

        .chat-tooltip::after {
            content: '';
            position: absolute;
            left: 100%;
            top: 50%;
            transform: translateY(-50%);
            border: 6px solid transparent;
            border-left-color: var(--text-color);
        }

        .floating-chat:hover .chat-tooltip {
            opacity: 1;
            visibility: visible;
            right: 75px;
        }

        @keyframes pulse-chat {
            0% {
                box-shadow: 0 8px 25px rgba(0, 136, 204, 0.3), 0 0 0 0 rgba(0, 136, 204, 0.7);
            }
            70% {
                box-shadow: 0 8px 25px rgba(0, 136, 204, 0.3), 0 0 0 10px rgba(0, 136, 204, 0);
            }
            100% {
                box-shadow: 0 8px 25px rgba(0, 136, 204, 0.3), 0 0 0 0 rgba(0, 136, 204, 0);
            }
        }

        /* Chat notification dot */
        .chat-notification {
            position: absolute;
            top: -5px;
            right: -5px;
            width: 20px;
            height: 20px;
            background: #ff4757;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.7rem;
            color: white;
            font-weight: bold;
            animation: bounce-notification 1s infinite;
        }

        @keyframes bounce-notification {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-5px);
            }
            60% {
                transform: translateY(-3px);
            }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }

            .search-container {
                order: 3;
                max-width: 100%;
            }

            .hero h1 {
                font-size: 2rem;
                font-weight: 800;
            }

            .categories {
                gap: 1rem;
            }

            .products-grid {
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 1rem;
            }

            .section-title {
                font-size: 2rem;
            }

            /* Mobile adjustments for floating chat */
            .floating-chat {
                bottom: 20px;
                right: 20px;
                width: 55px;
                height: 55px;
            }

            .floating-chat i {
                font-size: 1.3rem;
            }

            .chat-tooltip {
                display: none; /* Hide tooltip on mobile */
            }

            .chat-notification {
                width: 18px;
                height: 18px;
                font-size: 0.6rem;
            }

            /* Mobile adjustments for avatar selection */
            .avatar-selection {
                grid-template-columns: repeat(3, 1fr);
                gap: 8px;
            }

            .avatar-option {
                width: 50px;
                height: 50px;
                font-size: 1.5rem;
            }

            .user-avatar {
                width: 30px;
                height: 30px;
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header>
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-store"></i>
                    متجر النخبة
                </div>

                <div class="search-container">
                    <input type="text" class="search-box" placeholder="ابحث عن المنتجات..." id="searchInput">
                </div>

                <div class="header-actions">
                    <button class="theme-toggle" id="themeToggle" title="تبديل الوضع">
                        <i class="fas fa-moon"></i>
                    </button>

                    <button class="user-btn" id="userBtn" title="حسابي">
                        <i class="fas fa-user"></i>
                        <span class="user-name" id="userName" style="display: none;"></span>
                    </button>

                    <button class="wishlist-btn" id="wishlistBtn" title="قائمة الرغبات">
                        <i class="fas fa-heart"></i>
                        <span class="badge" id="wishlistCount">0</span>
                    </button>

                    <button class="cart-btn" id="cartBtn" title="عربة التسوق">
                        <i class="fas fa-shopping-cart"></i>
                        <span class="badge" id="cartCount">0</span>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Navigation Categories -->
    <nav class="nav-categories">
        <div class="container">
            <div class="categories">
                <button class="category-btn active" data-category="all">جميع المنتجات</button>
                <button class="category-btn" data-category="electronics">إلكترونيات</button>
                <button class="category-btn" data-category="fashion">أزياء</button>
                <button class="category-btn" data-category="home">منزل ومطبخ</button>
                <button class="category-btn" data-category="sports">رياضة</button>
                <button class="category-btn" data-category="books">كتب</button>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <div class="hero-content">
                <h1>مرحباً بك في متجر النخبة</h1>
                <p>اكتشف أفضل المنتجات بأسعار لا تُقاوم مع خدمة عملاء متميزة</p>
                <button class="cta-btn" onclick="document.querySelector('.products-section').scrollIntoView({behavior: 'smooth'})">
                    تسوق الآن
                </button>
            </div>
        </div>
    </section>

    <!-- Products Section -->
    <section class="products-section">
        <div class="container">
            <h2 class="section-title">منتجاتنا المميزة</h2>

            <div class="filters">
                <button class="filter-btn active" data-filter="all">الكل</button>
                <button class="filter-btn" data-filter="sale">عروض خاصة</button>
                <button class="filter-btn" data-filter="new">جديد</button>
                <button class="filter-btn" data-filter="popular">الأكثر مبيعاً</button>
            </div>

            <div class="products-grid" id="productsGrid">
                <!-- Products will be dynamically generated here -->
            </div>
        </div>
    </section>

    <!-- Floating Chat Button -->
    <div class="floating-chat" id="floatingChat" onclick="openTelegram()">
        <i class="fab fa-telegram-plane"></i>
        <div class="chat-tooltip">تحدث معنا على التليجرام</div>
        <div class="chat-notification" id="chatNotification">!</div>
    </div>
    <script>
        // Sample Products Data
        const products = [
            {
                id: 1,
                title: "هاتف ذكي متطور",
                description: "هاتف ذكي بمواصفات عالية وكاميرا متقدمة",
                price: 12500,
                oldPrice: 15000,
                category: "electronics",
                rating: 4.5,
                reviews: 128,
                badge: "جديد",
                icon: "📱",
                tags: ["sale", "new", "popular"]
            },
            {
                id: 2,
                title: "لابتوب للألعاب",
                description: "لابتوب قوي مخصص للألعاب والتصميم",
                price: 42500,
                oldPrice: 50000,
                category: "electronics",
                rating: 4.8,
                reviews: 89,
                badge: "عرض خاص",
                icon: "💻",
                tags: ["sale", "popular"]
            },
            {
                id: 3,
                title: "قميص قطني فاخر",
                description: "قميص قطني عالي الجودة ومريح",
                price: 750,
                oldPrice: 1000,
                category: "fashion",
                rating: 4.2,
                reviews: 45,
                badge: "خصم 25%",
                icon: "👔",
                tags: ["sale"]
            },
            {
                id: 4,
                title: "حذاء رياضي",
                description: "حذاء رياضي مريح للجري والتمارين",
                price: 1750,
                oldPrice: 2250,
                category: "sports",
                rating: 4.6,
                reviews: 67,
                badge: "الأكثر مبيعاً",
                icon: "👟",
                tags: ["popular", "sale"]
            },
            {
                id: 5,
                title: "مجموعة أواني طبخ",
                description: "مجموعة أواني طبخ من الستانلس ستيل",
                price: 3750,
                oldPrice: 4500,
                category: "home",
                rating: 4.4,
                reviews: 92,
                badge: "عرض محدود",
                icon: "🍳",
                tags: ["sale", "new"]
            },
            {
                id: 6,
                title: "كتاب تطوير الذات",
                description: "كتاب ملهم لتطوير الذات والنجاح",
                price: 400,
                oldPrice: 500,
                category: "books",
                rating: 4.7,
                reviews: 156,
                badge: "الأكثر قراءة",
                icon: "📚",
                tags: ["popular"]
            },
            {
                id: 7,
                title: "ساعة ذكية",
                description: "ساعة ذكية لتتبع اللياقة والصحة",
                price: 6000,
                oldPrice: 7500,
                category: "electronics",
                rating: 4.3,
                reviews: 73,
                badge: "جديد",
                icon: "⌚",
                tags: ["new", "sale"]
            },
            {
                id: 8,
                title: "فستان أنيق",
                description: "فستان أنيق للمناسبات الخاصة",
                price: 1400,
                oldPrice: 1750,
                category: "fashion",
                rating: 4.5,
                reviews: 34,
                badge: "تصميم حصري",
                icon: "👗",
                tags: ["new"]
            }
        ];

        // Application State
        let cart = JSON.parse(localStorage.getItem('cart')) || [];
        let wishlist = JSON.parse(localStorage.getItem('wishlist')) || [];
        let currentFilter = 'all';
        let currentCategory = 'all';
        let searchQuery = '';
        let currentUser = JSON.parse(localStorage.getItem('currentUser')) || null;
        let users = JSON.parse(localStorage.getItem('users')) || [];
        let selectedAvatar = '👤'; // Default avatar

        // Available Avatars
        const avatars = [
            '👤', '👨', '👩', '🧑',
            '👨‍💼', '👩‍💼', '👨‍🎓', '👩‍🎓',
            '👨‍💻', '👩‍💻', '👨‍🔬', '👩‍🔬',
            '👨‍🎨', '👩‍🎨', '👨‍🍳', '👩‍🍳',
            '🧔', '👱‍♂️', '👱‍♀️', '👨‍🦱',
            '👩‍🦱', '👨‍🦳', '👩‍🦳', '👨‍🦲',
            '👩‍🦲', '🧓', '👴', '👵',
            '😊', '😎', '🤓', '😇',
            '🥳', '🤩', '😋', '🙂'
        ];

        // User Database Management
        class UserDatabase {
            static addUser(userData) {
                const users = JSON.parse(localStorage.getItem('users')) || [];
                const newUser = {
                    id: Date.now(),
                    ...userData,
                    registrationDate: new Date().toISOString(),
                    orders: [],
                    totalSpent: 0
                };
                users.push(newUser);
                localStorage.setItem('users', JSON.stringify(users));
                return newUser;
            }

            static findUser(email) {
                const users = JSON.parse(localStorage.getItem('users')) || [];
                return users.find(user => user.email === email);
            }

            static updateUser(userId, updateData) {
                const users = JSON.parse(localStorage.getItem('users')) || [];
                const userIndex = users.findIndex(user => user.id === userId);
                if (userIndex !== -1) {
                    users[userIndex] = { ...users[userIndex], ...updateData };
                    localStorage.setItem('users', JSON.stringify(users));
                    return users[userIndex];
                }
                return null;
            }

            static getAllUsers() {
                return JSON.parse(localStorage.getItem('users')) || [];
            }

            static deleteUser(userId) {
                const users = JSON.parse(localStorage.getItem('users')) || [];
                const filteredUsers = users.filter(user => user.id !== userId);
                localStorage.setItem('users', JSON.stringify(filteredUsers));
            }
        }

        // DOM Elements
        const productsGrid = document.getElementById('productsGrid');
        const cartBtn = document.getElementById('cartBtn');
        const cartCount = document.getElementById('cartCount');
        const wishlistBtn = document.getElementById('wishlistBtn');
        const wishlistCount = document.getElementById('wishlistCount');
        const themeToggle = document.getElementById('themeToggle');
        const searchInput = document.getElementById('searchInput');
        const userBtn = document.getElementById('userBtn');
        const userName = document.getElementById('userName');

        // Initialize App
        document.addEventListener('DOMContentLoaded', function() {
            renderProducts();
            updateCartCount();
            updateWishlistCount();
            updateUserInterface();
            initializeEventListeners();
            loadTheme();
        });

        // Event Listeners
        function initializeEventListeners() {
            // Theme Toggle
            themeToggle.addEventListener('click', toggleTheme);

            // Search
            searchInput.addEventListener('input', handleSearch);

            // Category Buttons
            document.querySelectorAll('.category-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    document.querySelectorAll('.category-btn').forEach(b => b.classList.remove('active'));
                    e.target.classList.add('active');
                    currentCategory = e.target.dataset.category;
                    renderProducts();
                });
            });

            // Filter Buttons
            document.querySelectorAll('.filter-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                    e.target.classList.add('active');
                    currentFilter = e.target.dataset.filter;
                    renderProducts();
                });
            });

            // Cart Button
            cartBtn.addEventListener('click', showCart);

            // Wishlist Button
            wishlistBtn.addEventListener('click', showWishlist);

            // User Button
            userBtn.addEventListener('click', handleUserAction);
        }

        // Theme Functions
        function toggleTheme() {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

            document.documentElement.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);

            const icon = themeToggle.querySelector('i');
            icon.className = newTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
        }

        function loadTheme() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.documentElement.setAttribute('data-theme', savedTheme);

            const icon = themeToggle.querySelector('i');
            icon.className = savedTheme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
        }

        // User Management Functions
        function updateUserInterface() {
            if (currentUser) {
                // Remove existing avatar if any
                const existingAvatar = userBtn.querySelector('.user-avatar');
                if (existingAvatar) {
                    existingAvatar.remove();
                }

                // Add user avatar
                const avatar = document.createElement('div');
                avatar.className = 'user-avatar';
                avatar.textContent = currentUser.avatar || '👤';
                userBtn.insertBefore(avatar, userBtn.firstChild);

                userName.textContent = currentUser.firstName;
                userName.style.display = 'inline';
                userBtn.classList.add('logged-in');
                userBtn.title = `مرحباً ${currentUser.firstName}`;
            } else {
                // Remove avatar
                const existingAvatar = userBtn.querySelector('.user-avatar');
                if (existingAvatar) {
                    existingAvatar.remove();
                }

                userName.style.display = 'none';
                userBtn.classList.remove('logged-in');
                userBtn.title = 'تسجيل الدخول';
            }
        }

        function handleUserAction() {
            if (currentUser) {
                showUserMenu();
            } else {
                showLoginForm();
            }
        }

        function showLoginForm() {
            showModal(`
                <h2 style="text-align: center; margin-bottom: 20px;">🔐 تسجيل الدخول</h2>
                <div style="display: flex; gap: 20px; margin-bottom: 20px;">
                    <button onclick="showLoginTab()" id="loginTab" style="flex: 1; padding: 10px; border: none; background: var(--primary-color); color: white; border-radius: 5px; cursor: pointer;">
                        تسجيل الدخول
                    </button>
                    <button onclick="showRegisterTab()" id="registerTab" style="flex: 1; padding: 10px; border: 1px solid var(--border-color); background: transparent; color: var(--text-color); border-radius: 5px; cursor: pointer;">
                        إنشاء حساب جديد
                    </button>
                </div>

                <div id="loginForm">
                    <form onsubmit="handleLogin(event)" style="display: flex; flex-direction: column; gap: 15px;">
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">البريد الإلكتروني:</label>
                            <input type="email" id="loginEmail" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; font-family: 'Cairo', sans-serif;">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">كلمة المرور:</label>
                            <input type="password" id="loginPassword" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; font-family: 'Cairo', sans-serif;">
                        </div>
                        <button type="submit" style="background: var(--primary-color); color: white; border: none; padding: 15px; border-radius: 10px; cursor: pointer; font-size: 1.1em; font-family: 'Cairo', sans-serif; font-weight: 600;">
                            تسجيل الدخول
                        </button>
                    </form>
                </div>

                <div id="registerForm" style="display: none;">
                    <form onsubmit="handleRegister(event)" style="display: flex; flex-direction: column; gap: 15px;">
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">اختر صورتك الرمزية:</label>
                            <div class="avatar-selection" id="avatarSelection">
                                ${generateAvatarOptions()}
                            </div>
                        </div>
                        <div style="display: flex; gap: 10px;">
                            <div style="flex: 1;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">الاسم الأول:</label>
                                <input type="text" id="firstName" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; font-family: 'Cairo', sans-serif;">
                            </div>
                            <div style="flex: 1;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">الاسم الأخير:</label>
                                <input type="text" id="lastName" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; font-family: 'Cairo', sans-serif;">
                            </div>
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">البريد الإلكتروني:</label>
                            <input type="email" id="registerEmail" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; font-family: 'Cairo', sans-serif;">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">رقم الهاتف:</label>
                            <input type="tel" id="phone" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; font-family: 'Cairo', sans-serif;">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">كلمة المرور:</label>
                            <input type="password" id="registerPassword" required minlength="6" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; font-family: 'Cairo', sans-serif;">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">تأكيد كلمة المرور:</label>
                            <input type="password" id="confirmPassword" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; font-family: 'Cairo', sans-serif;">
                        </div>
                        <button type="submit" style="background: var(--accent-color); color: white; border: none; padding: 15px; border-radius: 10px; cursor: pointer; font-size: 1.1em; font-family: 'Cairo', sans-serif; font-weight: 600;">
                            إنشاء حساب جديد
                        </button>
                    </form>
                </div>
            `);
        }

        function showLoginTab() {
            document.getElementById('loginTab').style.background = 'var(--primary-color)';
            document.getElementById('loginTab').style.color = 'white';
            document.getElementById('registerTab').style.background = 'transparent';
            document.getElementById('registerTab').style.color = 'var(--text-color)';
            document.getElementById('loginForm').style.display = 'block';
            document.getElementById('registerForm').style.display = 'none';
        }

        function showRegisterTab() {
            document.getElementById('registerTab').style.background = 'var(--accent-color)';
            document.getElementById('registerTab').style.color = 'white';
            document.getElementById('loginTab').style.background = 'transparent';
            document.getElementById('loginTab').style.color = 'var(--text-color)';
            document.getElementById('loginForm').style.display = 'none';
            document.getElementById('registerForm').style.display = 'block';

            // Initialize avatar selection after showing the form
            setTimeout(() => {
                initializeAvatarSelection();
            }, 100);
        }

        // Avatar Functions
        function generateAvatarOptions() {
            return avatars.map((avatar, index) =>
                `<div class="avatar-option ${index === 0 ? 'selected' : ''}" onclick="selectAvatar('${avatar}', this)" data-avatar="${avatar}">
                    ${avatar}
                </div>`
            ).join('');
        }

        function initializeAvatarSelection() {
            const avatarContainer = document.getElementById('avatarSelection');
            if (avatarContainer) {
                avatarContainer.innerHTML = generateAvatarOptions();
                selectedAvatar = avatars[0]; // Set default
            }
        }

        function selectAvatar(avatar, element) {
            // Remove selected class from all options
            document.querySelectorAll('.avatar-option').forEach(option => {
                option.classList.remove('selected');
            });

            // Add selected class to clicked option
            element.classList.add('selected');
            selectedAvatar = avatar;
        }

        function handleLogin(event) {
            event.preventDefault();
            const email = document.getElementById('loginEmail').value;
            const password = document.getElementById('loginPassword').value;

            const user = UserDatabase.findUser(email);
            if (user && user.password === password) {
                currentUser = user;
                localStorage.setItem('currentUser', JSON.stringify(currentUser));
                updateUserInterface();
                closeModal();
                showNotification(`مرحباً بعودتك ${user.firstName}! 👋`, 'success');
            } else {
                showNotification('البريد الإلكتروني أو كلمة المرور غير صحيحة', 'error');
            }
        }

        function handleRegister(event) {
            event.preventDefault();
            const firstName = document.getElementById('firstName').value;
            const lastName = document.getElementById('lastName').value;
            const email = document.getElementById('registerEmail').value;
            const phone = document.getElementById('phone').value;
            const password = document.getElementById('registerPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;

            if (password !== confirmPassword) {
                showNotification('كلمة المرور غير متطابقة', 'error');
                return;
            }

            if (UserDatabase.findUser(email)) {
                showNotification('البريد الإلكتروني مسجل مسبقاً', 'error');
                return;
            }

            const newUser = UserDatabase.addUser({
                firstName,
                lastName,
                email,
                phone,
                password,
                avatar: selectedAvatar
            });

            currentUser = newUser;
            localStorage.setItem('currentUser', JSON.stringify(currentUser));
            updateUserInterface();
            closeModal();
            showNotification(`مرحباً ${firstName}! تم إنشاء حسابك بنجاح 🎉`, 'success');
        }

        function showUserMenu() {
            const totalOrders = currentUser.orders.length;
            const formattedSpent = currentUser.totalSpent.toLocaleString();

            showModal(`
                <h2 style="text-align: center; margin-bottom: 20px;">👤 حسابي</h2>
                <div style="text-align: center; margin-bottom: 20px;">
                    <div style="width: 80px; height: 80px; border-radius: 50%; background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)); display: flex; align-items: center; justify-content: center; font-size: 3rem; margin: 0 auto 10px; box-shadow: var(--shadow);">
                        ${currentUser.avatar || '👤'}
                    </div>
                    <h3>${currentUser.firstName} ${currentUser.lastName}</h3>
                </div>
                <div style="background: var(--card-bg); padding: 20px; border-radius: 10px; margin-bottom: 20px;">
                    <h3 style="margin-bottom: 15px;">معلومات الحساب</h3>
                    <p><strong>الاسم:</strong> ${currentUser.firstName} ${currentUser.lastName}</p>
                    <p><strong>البريد الإلكتروني:</strong> ${currentUser.email}</p>
                    <p><strong>رقم الهاتف:</strong> ${currentUser.phone}</p>
                    <p><strong>تاريخ التسجيل:</strong> ${new Date(currentUser.registrationDate).toLocaleDateString('ar-EG')}</p>
                </div>

                <div style="background: var(--card-bg); padding: 20px; border-radius: 10px; margin-bottom: 20px;">
                    <h3 style="margin-bottom: 15px;">إحصائيات التسوق</h3>
                    <p><strong>عدد الطلبات:</strong> ${totalOrders}</p>
                    <p><strong>إجمالي المشتريات:</strong> ${formattedSpent} جنيه</p>
                </div>

                <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                    <button onclick="showOrderHistory()" style="flex: 1; background: var(--primary-color); color: white; border: none; padding: 15px; border-radius: 10px; cursor: pointer; font-family: 'Cairo', sans-serif; font-weight: 600;">
                        تاريخ الطلبات
                    </button>
                    <button onclick="editProfile()" style="flex: 1; background: var(--secondary-color); color: white; border: none; padding: 15px; border-radius: 10px; cursor: pointer; font-family: 'Cairo', sans-serif; font-weight: 600;">
                        تعديل الملف الشخصي
                    </button>
                    <button onclick="logout()" style="flex: 1; background: #ef4444; color: white; border: none; padding: 15px; border-radius: 10px; cursor: pointer; font-family: 'Cairo', sans-serif; font-weight: 600;">
                        تسجيل الخروج
                    </button>
                </div>
            `);
        }

        function logout() {
            currentUser = null;
            localStorage.removeItem('currentUser');
            updateUserInterface();
            closeModal();
            showNotification('تم تسجيل الخروج بنجاح', 'info');
        }

        function showOrderHistory() {
            if (!currentUser || currentUser.orders.length === 0) {
                showNotification('لا توجد طلبات سابقة', 'info');
                return;
            }

            const orderItems = currentUser.orders.map(order => `
                <div style="background: var(--card-bg); padding: 15px; border-radius: 10px; margin-bottom: 15px;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                        <strong>طلب رقم: ${order.id}</strong>
                        <span style="color: #666;">${new Date(order.date).toLocaleDateString('ar-EG')}</span>
                    </div>
                    <div style="margin-bottom: 10px;">
                        <strong>المنتجات:</strong>
                        <ul style="margin: 5px 0; padding-right: 20px;">
                            ${order.items.map(item => `<li>${item.title} × ${item.quantity}</li>`).join('')}
                        </ul>
                    </div>
                    <div style="display: flex; justify-content: space-between;">
                        <span><strong>المجموع:</strong> ${order.total.toLocaleString()} جنيه</span>
                        <span style="background: ${order.status === 'completed' ? '#10b981' : '#f59e0b'}; color: white; padding: 5px 10px; border-radius: 5px; font-size: 0.9rem;">
                            ${order.status === 'completed' ? 'مكتمل' : 'قيد المعالجة'}
                        </span>
                    </div>
                </div>
            `).join('');

            showModal(`
                <h2 style="text-align: center; margin-bottom: 20px;">📋 تاريخ الطلبات</h2>
                <div style="max-height: 400px; overflow-y: auto;">
                    ${orderItems}
                </div>
            `);
        }

        function editProfile() {
            selectedAvatar = currentUser.avatar || '👤'; // Set current avatar as selected

            showModal(`
                <h2 style="text-align: center; margin-bottom: 20px;">✏️ تعديل الملف الشخصي</h2>
                <form onsubmit="updateProfile(event)" style="display: flex; flex-direction: column; gap: 15px;">
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">تغيير الصورة الرمزية:</label>
                        <div class="avatar-selection" id="editAvatarSelection">
                            ${generateEditAvatarOptions()}
                        </div>
                    </div>
                    <div style="display: flex; gap: 10px;">
                        <div style="flex: 1;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">الاسم الأول:</label>
                            <input type="text" id="editFirstName" value="${currentUser.firstName}" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; font-family: 'Cairo', sans-serif;">
                        </div>
                        <div style="flex: 1;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">الاسم الأخير:</label>
                            <input type="text" id="editLastName" value="${currentUser.lastName}" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; font-family: 'Cairo', sans-serif;">
                        </div>
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">البريد الإلكتروني:</label>
                        <input type="email" id="editEmail" value="${currentUser.email}" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; font-family: 'Cairo', sans-serif;">
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">رقم الهاتف:</label>
                        <input type="tel" id="editPhone" value="${currentUser.phone}" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; font-family: 'Cairo', sans-serif;">
                    </div>
                    <div style="display: flex; gap: 10px;">
                        <button type="button" onclick="closeModal()" style="flex: 1; background: #6b7280; color: white; border: none; padding: 15px; border-radius: 10px; cursor: pointer; font-family: 'Cairo', sans-serif; font-weight: 600;">
                            إلغاء
                        </button>
                        <button type="submit" style="flex: 1; background: var(--primary-color); color: white; border: none; padding: 15px; border-radius: 10px; cursor: pointer; font-family: 'Cairo', sans-serif; font-weight: 600;">
                            حفظ التغييرات
                        </button>
                    </div>
                </form>
            `);
        }

        function generateEditAvatarOptions() {
            return avatars.map((avatar) =>
                `<div class="avatar-option ${avatar === currentUser.avatar ? 'selected' : ''}" onclick="selectAvatar('${avatar}', this)" data-avatar="${avatar}">
                    ${avatar}
                </div>`
            ).join('');
        }

        function updateProfile(event) {
            event.preventDefault();
            const firstName = document.getElementById('editFirstName').value;
            const lastName = document.getElementById('editLastName').value;
            const email = document.getElementById('editEmail').value;
            const phone = document.getElementById('editPhone').value;

            // Check if email is already taken by another user
            const existingUser = UserDatabase.findUser(email);
            if (existingUser && existingUser.id !== currentUser.id) {
                showNotification('البريد الإلكتروني مستخدم من قبل مستخدم آخر', 'error');
                return;
            }

            const updatedUser = UserDatabase.updateUser(currentUser.id, {
                firstName,
                lastName,
                email,
                phone,
                avatar: selectedAvatar
            });

            if (updatedUser) {
                currentUser = updatedUser;
                localStorage.setItem('currentUser', JSON.stringify(currentUser));
                updateUserInterface();
                closeModal();
                showNotification('تم تحديث الملف الشخصي بنجاح', 'success');
            } else {
                showNotification('حدث خطأ أثناء التحديث', 'error');
            }
        }

        // Search Function
        function handleSearch(e) {
            searchQuery = e.target.value.toLowerCase();
            renderProducts();
        }

        // Product Rendering
        function renderProducts() {
            let filteredProducts = products;

            // Filter by category
            if (currentCategory !== 'all') {
                filteredProducts = filteredProducts.filter(product =>
                    product.category === currentCategory
                );
            }

            // Filter by tags
            if (currentFilter !== 'all') {
                filteredProducts = filteredProducts.filter(product =>
                    product.tags.includes(currentFilter)
                );
            }

            // Filter by search query
            if (searchQuery) {
                filteredProducts = filteredProducts.filter(product =>
                    product.title.toLowerCase().includes(searchQuery) ||
                    product.description.toLowerCase().includes(searchQuery)
                );
            }

            productsGrid.innerHTML = filteredProducts.map(product => createProductCard(product)).join('');
        }

        function createProductCard(product) {
            const isInWishlist = wishlist.some(item => item.id === product.id);
            const discount = Math.round(((product.oldPrice - product.price) / product.oldPrice) * 100);

            return `
                <div class="product-card" data-product-id="${product.id}">
                    <div class="product-image">
                        ${product.icon}
                        <div class="product-badge">${product.badge}</div>
                        <button class="wishlist-heart ${isInWishlist ? 'active' : ''}" onclick="toggleWishlist(${product.id})">
                            <i class="fas fa-heart"></i>
                        </button>
                    </div>
                    <div class="product-info">
                        <h3 class="product-title">${product.title}</h3>
                        <p class="product-description">${product.description}</p>
                        <div class="product-rating">
                            <div class="stars">
                                ${generateStars(product.rating)}
                            </div>
                            <span class="rating-text">(${product.reviews} تقييم)</span>
                        </div>
                        <div class="product-price">
                            <span class="current-price">${product.price.toLocaleString()} جنيه</span>
                            ${product.oldPrice ? `<span class="old-price">${product.oldPrice.toLocaleString()} جنيه</span>` : ''}
                            ${discount > 0 ? `<span class="discount">-${discount}%</span>` : ''}
                        </div>
                        <div class="product-actions">
                            <button class="add-to-cart" onclick="addToCart(${product.id})">
                                <i class="fas fa-cart-plus"></i> أضف للسلة
                            </button>
                            <button class="quick-view" onclick="quickView(${product.id})">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `;
        }

        function generateStars(rating) {
            const fullStars = Math.floor(rating);
            const hasHalfStar = rating % 1 !== 0;
            let stars = '';

            for (let i = 0; i < fullStars; i++) {
                stars += '<i class="fas fa-star"></i>';
            }

            if (hasHalfStar) {
                stars += '<i class="fas fa-star-half-alt"></i>';
            }

            const emptyStars = 5 - Math.ceil(rating);
            for (let i = 0; i < emptyStars; i++) {
                stars += '<i class="far fa-star"></i>';
            }

            return stars;
        }

        // Cart Functions
        function addToCart(productId) {
            const product = products.find(p => p.id === productId);
            const existingItem = cart.find(item => item.id === productId);

            if (existingItem) {
                existingItem.quantity += 1;
            } else {
                cart.push({ ...product, quantity: 1 });
            }

            localStorage.setItem('cart', JSON.stringify(cart));
            updateCartCount();
            showNotification('تم إضافة المنتج إلى السلة', 'success');

            // Add animation to cart button
            cartBtn.classList.add('pulse');
            setTimeout(() => cartBtn.classList.remove('pulse'), 1000);
        }

        function updateCartCount() {
            const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
            cartCount.textContent = totalItems;
        }

        function showCart() {
            if (cart.length === 0) {
                showNotification('السلة فارغة', 'info');
                return;
            }

            const cartItems = cart.map(item => `
                <div style="display: flex; justify-content: space-between; align-items: center; padding: 10px; border-bottom: 1px solid #eee;">
                    <div>
                        <strong>${item.title}</strong><br>
                        <small>${item.price.toLocaleString()} جنيه × ${item.quantity}</small>
                    </div>
                    <div>
                        <button onclick="removeFromCart(${item.id})" style="background: #ef4444; color: white; border: none; padding: 5px 10px; border-radius: 5px; cursor: pointer;">
                            حذف
                        </button>
                    </div>
                </div>
            `).join('');

            const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            const tax = Math.round(total * 0.14); // ضريبة القيمة المضافة في مصر 14%
            const shipping = total > 2500 ? 0 : 250; // شحن مجاني للطلبات فوق 2500 جنيه
            const finalTotal = total + tax + shipping;

            showModal(`
                <h2 style="text-align: center; margin-bottom: 20px;">🛒 عربة التسوق</h2>
                <div style="max-height: 300px; overflow-y: auto;">
                    ${cartItems}
                </div>
                <div style="margin-top: 20px; padding: 20px; background: #f9f9f9; border-radius: 10px;">
                    <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                        <span>المجموع الفرعي:</span>
                        <span>${total.toLocaleString()} جنيه</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                        <span>ضريبة القيمة المضافة (14%):</span>
                        <span>${tax.toLocaleString()} جنيه</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                        <span>الشحن:</span>
                        <span>${shipping === 0 ? 'مجاني' : shipping.toLocaleString() + ' جنيه'}</span>
                    </div>
                    <hr>
                    <div style="display: flex; justify-content: space-between; font-weight: bold; font-size: 1.2em;">
                        <span>المجموع النهائي:</span>
                        <span>${finalTotal.toLocaleString()} جنيه</span>
                    </div>
                </div>
                <div style="margin-top: 20px; display: flex; gap: 10px;">
                    <button onclick="clearCart()" style="flex: 1; background: #ef4444; color: white; border: none; padding: 15px; border-radius: 10px; cursor: pointer;">
                        إفراغ السلة
                    </button>
                    <button onclick="checkout()" style="flex: 2; background: #10b981; color: white; border: none; padding: 15px; border-radius: 10px; cursor: pointer;">
                        إتمام الشراء
                    </button>
                </div>
            `);
        }

        function removeFromCart(productId) {
            cart = cart.filter(item => item.id !== productId);
            localStorage.setItem('cart', JSON.stringify(cart));
            updateCartCount();
            showNotification('تم حذف المنتج من السلة', 'info');
            showCart(); // Refresh cart display
        }

        function clearCart() {
            cart = [];
            localStorage.setItem('cart', JSON.stringify(cart));
            updateCartCount();
            closeModal();
            showNotification('تم إفراغ السلة', 'info');
        }

        function checkout() {
            if (cart.length === 0) {
                showNotification('السلة فارغة', 'error');
                return;
            }

            closeModal();
            showModal(`
                <h2 style="text-align: center; margin-bottom: 20px;">💳 إتمام الشراء</h2>
                <form onsubmit="processPayment(event)" style="display: flex; flex-direction: column; gap: 15px;">
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">الاسم الكامل:</label>
                        <input type="text" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">رقم الهاتف:</label>
                        <input type="tel" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">العنوان:</label>
                        <textarea required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; min-height: 80px;"></textarea>
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: bold;">طريقة الدفع:</label>
                        <select required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                            <option value="">اختر طريقة الدفع</option>
                            <option value="card">بطاقة ائتمان</option>
                            <option value="cash">الدفع عند الاستلام</option>
                            <option value="bank">تحويل بنكي</option>
                        </select>
                    </div>
                    <button type="submit" style="background: #10b981; color: white; border: none; padding: 15px; border-radius: 10px; cursor: pointer; font-size: 1.1em;">
                        تأكيد الطلب
                    </button>
                </form>
            `);
        }

        function processPayment(event) {
            event.preventDefault();
            closeModal();

            // Simulate payment processing
            showNotification('جاري معالجة الطلب...', 'info');

            setTimeout(() => {
                const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
                const tax = Math.round(total * 0.14);
                const shipping = total > 2500 ? 0 : 250;
                const finalTotal = total + tax + shipping;

                // Create order object
                const order = {
                    id: Date.now(),
                    date: new Date().toISOString(),
                    items: [...cart],
                    subtotal: total,
                    tax: tax,
                    shipping: shipping,
                    total: finalTotal,
                    status: 'processing'
                };

                // Update user's order history and total spent if logged in
                if (currentUser) {
                    currentUser.orders.push(order);
                    currentUser.totalSpent += finalTotal;
                    UserDatabase.updateUser(currentUser.id, {
                        orders: currentUser.orders,
                        totalSpent: currentUser.totalSpent
                    });
                    localStorage.setItem('currentUser', JSON.stringify(currentUser));
                }

                clearCart();
                showNotification('تم تأكيد طلبك بنجاح! سيتم التواصل معك قريباً', 'success');

                // Show order confirmation
                setTimeout(() => {
                    showModal(`
                        <h2 style="text-align: center; margin-bottom: 20px;">✅ تم تأكيد الطلب</h2>
                        <div style="text-align: center; margin-bottom: 20px;">
                            <div style="font-size: 4rem; margin-bottom: 10px;">🎉</div>
                            <h3>شكراً لك على طلبك!</h3>
                            <p>رقم الطلب: <strong>${order.id}</strong></p>
                            <p>المجموع النهائي: <strong>${finalTotal.toLocaleString()} جنيه</strong></p>
                        </div>
                        <div style="background: var(--card-bg); padding: 15px; border-radius: 10px; margin-bottom: 20px;">
                            <h4>تفاصيل الطلب:</h4>
                            <ul style="margin: 10px 0; padding-right: 20px;">
                                ${cart.map(item => `<li>${item.title} × ${item.quantity}</li>`).join('')}
                            </ul>
                        </div>
                        <p style="text-align: center; color: #666;">سيتم التواصل معك خلال 24 ساعة لتأكيد الطلب وترتيب التوصيل</p>
                    `);
                }, 1000);
            }, 2000);
        }

        // Wishlist Functions
        function toggleWishlist(productId) {
            const product = products.find(p => p.id === productId);
            const existingIndex = wishlist.findIndex(item => item.id === productId);

            if (existingIndex > -1) {
                wishlist.splice(existingIndex, 1);
                showNotification('تم حذف المنتج من قائمة الرغبات', 'info');
            } else {
                wishlist.push(product);
                showNotification('تم إضافة المنتج إلى قائمة الرغبات', 'success');
            }

            localStorage.setItem('wishlist', JSON.stringify(wishlist));
            updateWishlistCount();
            renderProducts(); // Re-render to update heart icons
        }

        function updateWishlistCount() {
            wishlistCount.textContent = wishlist.length;
        }

        function showWishlist() {
            if (wishlist.length === 0) {
                showNotification('قائمة الرغبات فارغة', 'info');
                return;
            }

            const wishlistItems = wishlist.map(item => `
                <div style="display: flex; justify-content: space-between; align-items: center; padding: 15px; border-bottom: 1px solid #eee;">
                    <div style="display: flex; align-items: center; gap: 15px;">
                        <div style="font-size: 2rem;">${item.icon}</div>
                        <div>
                            <strong>${item.title}</strong><br>
                            <small style="color: #666;">${item.description}</small><br>
                            <span style="color: #2563eb; font-weight: bold;">${item.price.toLocaleString()} جنيه</span>
                        </div>
                    </div>
                    <div style="display: flex; gap: 10px;">
                        <button onclick="addToCart(${item.id})" style="background: #10b981; color: white; border: none; padding: 8px 12px; border-radius: 5px; cursor: pointer;">
                            أضف للسلة
                        </button>
                        <button onclick="toggleWishlist(${item.id}); showWishlist();" style="background: #ef4444; color: white; border: none; padding: 8px 12px; border-radius: 5px; cursor: pointer;">
                            حذف
                        </button>
                    </div>
                </div>
            `).join('');

            showModal(`
                <h2 style="text-align: center; margin-bottom: 20px;">❤️ قائمة الرغبات</h2>
                <div style="max-height: 400px; overflow-y: auto;">
                    ${wishlistItems}
                </div>
                <div style="margin-top: 20px; text-align: center;">
                    <button onclick="clearWishlist()" style="background: #ef4444; color: white; border: none; padding: 15px 30px; border-radius: 10px; cursor: pointer;">
                        إفراغ قائمة الرغبات
                    </button>
                </div>
            `);
        }

        function clearWishlist() {
            wishlist = [];
            localStorage.setItem('wishlist', JSON.stringify(wishlist));
            updateWishlistCount();
            closeModal();
            showNotification('تم إفراغ قائمة الرغبات', 'info');
            renderProducts();
        }

        // Quick View Function
        function quickView(productId) {
            const product = products.find(p => p.id === productId);
            const isInWishlist = wishlist.some(item => item.id === productId);
            const discount = Math.round(((product.oldPrice - product.price) / product.oldPrice) * 100);

            showModal(`
                <div style="display: flex; gap: 20px; align-items: start;">
                    <div style="flex: 1; text-align: center;">
                        <div style="font-size: 8rem; margin-bottom: 20px;">${product.icon}</div>
                        <div style="background: ${isInWishlist ? '#e74c3c' : '#f0f0f0'}; color: ${isInWishlist ? 'white' : '#666'}; padding: 10px; border-radius: 10px; cursor: pointer;" onclick="toggleWishlist(${product.id}); quickView(${product.id});">
                            <i class="fas fa-heart"></i> ${isInWishlist ? 'في المفضلة' : 'أضف للمفضلة'}
                        </div>
                    </div>
                    <div style="flex: 2;">
                        <h2 style="margin-bottom: 10px;">${product.title}</h2>
                        <p style="color: #666; margin-bottom: 15px;">${product.description}</p>

                        <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 15px;">
                            <div style="color: #f59e0b;">
                                ${generateStars(product.rating)}
                            </div>
                            <span style="color: #666;">(${product.reviews} تقييم)</span>
                        </div>

                        <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 20px;">
                            <span style="font-size: 1.5rem; font-weight: bold; color: #2563eb;">${product.price.toLocaleString()} جنيه</span>
                            ${product.oldPrice ? `<span style="text-decoration: line-through; color: #666;">${product.oldPrice.toLocaleString()} جنيه</span>` : ''}
                            ${discount > 0 ? `<span style="background: #ef4444; color: white; padding: 5px 10px; border-radius: 5px; font-size: 0.9rem;">-${discount}%</span>` : ''}
                        </div>

                        <div style="background: #f9f9f9; padding: 15px; border-radius: 10px; margin-bottom: 20px;">
                            <h4 style="margin-bottom: 10px;">مميزات المنتج:</h4>
                            <ul style="margin: 0; padding-right: 20px;">
                                <li>جودة عالية ومضمونة</li>
                                <li>ضمان لمدة سنة كاملة</li>
                                <li>شحن مجاني للطلبات فوق 2500 جنيه</li>
                                <li>إمكانية الإرجاع خلال 14 يوم</li>
                            </ul>
                        </div>

                        <button onclick="addToCart(${product.id}); closeModal();" style="width: 100%; background: #10b981; color: white; border: none; padding: 15px; border-radius: 10px; cursor: pointer; font-size: 1.1rem;">
                            <i class="fas fa-cart-plus"></i> أضف إلى السلة
                        </button>
                    </div>
                </div>
            `);
        }

        // Modal Functions
        function showModal(content) {
            // Remove existing modal if any
            const existingModal = document.getElementById('modal');
            if (existingModal) {
                existingModal.remove();
            }

            const modal = document.createElement('div');
            modal.id = 'modal';
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
                animation: fadeIn 0.3s ease;
            `;

            const modalContent = document.createElement('div');
            modalContent.style.cssText = `
                background: var(--bg-color);
                color: var(--text-color);
                padding: 30px;
                border-radius: 15px;
                max-width: 90%;
                max-height: 90%;
                overflow-y: auto;
                position: relative;
                animation: slideIn 0.3s ease;
                box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            `;

            const closeBtn = document.createElement('button');
            closeBtn.innerHTML = '<i class="fas fa-times"></i>';
            closeBtn.style.cssText = `
                position: absolute;
                top: 15px;
                right: 15px;
                background: none;
                border: none;
                font-size: 1.5rem;
                cursor: pointer;
                color: var(--text-color);
                width: 40px;
                height: 40px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: all 0.3s ease;
            `;
            closeBtn.onmouseover = () => closeBtn.style.background = 'rgba(0, 0, 0, 0.1)';
            closeBtn.onmouseout = () => closeBtn.style.background = 'none';
            closeBtn.onclick = closeModal;

            modalContent.innerHTML = content;
            modalContent.appendChild(closeBtn);
            modal.appendChild(modalContent);
            document.body.appendChild(modal);

            // Close modal when clicking outside
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    closeModal();
                }
            });

            // Add CSS animations
            const style = document.createElement('style');
            style.textContent = `
                @keyframes fadeIn {
                    from { opacity: 0; }
                    to { opacity: 1; }
                }
                @keyframes slideIn {
                    from { transform: translateY(-50px); opacity: 0; }
                    to { transform: translateY(0); opacity: 1; }
                }
            `;
            if (!document.querySelector('style[data-modal]')) {
                style.setAttribute('data-modal', 'true');
                document.head.appendChild(style);
            }
        }

        function closeModal() {
            const modal = document.getElementById('modal');
            if (modal) {
                modal.style.animation = 'fadeOut 0.3s ease';
                setTimeout(() => modal.remove(), 300);
            }
        }

        // Notification System
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
                color: white;
                padding: 15px 20px;
                border-radius: 10px;
                z-index: 10001;
                animation: slideInRight 0.3s ease;
                box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                max-width: 300px;
                word-wrap: break-word;
            `;

            const icon = type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️';
            notification.innerHTML = `${icon} ${message}`;

            document.body.appendChild(notification);

            // Auto remove after 3 seconds
            setTimeout(() => {
                notification.style.animation = 'slideOutRight 0.3s ease';
                setTimeout(() => notification.remove(), 300);
            }, 3000);

            // Add CSS animations for notifications
            const style = document.createElement('style');
            style.textContent = `
                @keyframes slideInRight {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
                @keyframes slideOutRight {
                    from { transform: translateX(0); opacity: 1; }
                    to { transform: translateX(100%); opacity: 0; }
                }
                @keyframes fadeOut {
                    from { opacity: 1; }
                    to { opacity: 0; }
                }
            `;
            if (!document.querySelector('style[data-notifications]')) {
                style.setAttribute('data-notifications', 'true');
                document.head.appendChild(style);
            }
        }

        // Add smooth scrolling and loading animation
        document.addEventListener('DOMContentLoaded', function() {
            // Add loading animation
            document.body.style.opacity = '0';
            setTimeout(() => {
                document.body.style.transition = 'opacity 0.5s ease';
                document.body.style.opacity = '1';
            }, 100);

            // Add scroll animations for product cards
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.animation = 'fadeInUp 0.6s ease forwards';
                    }
                });
            }, observerOptions);

            // Observe product cards when they're rendered
            const originalRenderProducts = renderProducts;
            renderProducts = function() {
                originalRenderProducts();
                setTimeout(() => {
                    document.querySelectorAll('.product-card').forEach(card => {
                        card.style.opacity = '0';
                        card.style.transform = 'translateY(30px)';
                        observer.observe(card);
                    });
                }, 100);
            };

            // Welcome message
            setTimeout(() => {
                showNotification('مرحباً بك في متجر النخبة! 🛍️', 'success');
            }, 1000);
        });

        // Add keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // ESC to close modal
            if (e.key === 'Escape') {
                closeModal();
            }

            // Ctrl+F to focus search
            if (e.ctrlKey && e.key === 'f') {
                e.preventDefault();
                searchInput.focus();
            }
        });

        // Add scroll to top button
        window.addEventListener('scroll', function() {
            const scrollBtn = document.getElementById('scrollToTop');
            if (window.pageYOffset > 300) {
                if (!scrollBtn) {
                    const btn = document.createElement('button');
                    btn.id = 'scrollToTop';
                    btn.innerHTML = '<i class="fas fa-arrow-up"></i>';
                    btn.style.cssText = `
                        position: fixed;
                        bottom: 20px;
                        left: 20px;
                        background: var(--primary-color);
                        color: white;
                        border: none;
                        width: 50px;
                        height: 50px;
                        border-radius: 50%;
                        cursor: pointer;
                        z-index: 1000;
                        box-shadow: var(--shadow);
                        transition: all 0.3s ease;
                        animation: fadeIn 0.3s ease;
                    `;
                    btn.onclick = () => window.scrollTo({ top: 0, behavior: 'smooth' });
                    btn.onmouseover = () => btn.style.transform = 'scale(1.1)';
                    btn.onmouseout = () => btn.style.transform = 'scale(1)';
                    document.body.appendChild(btn);
                }
            } else if (scrollBtn) {
                scrollBtn.style.animation = 'fadeOut 0.3s ease';
                setTimeout(() => scrollBtn.remove(), 300);
            }
        });

        // Telegram Chat Functions
        function openTelegram() {
            // ⚠️ مهم: قم بتغيير اسم المستخدم إلى اسم المستخدم الخاص بك في التليجرام
            // مثال: إذا كان رابطك https://t.me/mystore123 فضع 'mystore123'
            const telegramUsername = 'YourTelegramUsername'; // ضع اسم المستخدم الخاص بك هنا
            const telegramUrl = `https://t.me/${telegramUsername}`;

            // رسالة ترحيبية مخصصة
            const welcomeMessage = encodeURIComponent('مرحباً! أريد الاستفسار عن منتجاتكم في متجر النخبة 🛍️');
            const fullUrl = `${telegramUrl}?text=${welcomeMessage}`;

            // فتح التليجرام في نافذة جديدة
            window.open(fullUrl, '_blank');

            // إخفاء نقطة الإشعار بعد النقر
            const notification = document.getElementById('chatNotification');
            if (notification) {
                notification.style.display = 'none';
            }

            // إظهار رسالة تأكيد
            showNotification('سيتم توجيهك إلى التليجرام للدردشة معنا! 💬', 'success');
        }

        // إظهار نقطة الإشعار بعد فترة من تحميل الصفحة
        setTimeout(() => {
            const notification = document.getElementById('chatNotification');
            if (notification) {
                notification.style.display = 'flex';
            }
        }, 5000); // تظهر بعد 5 ثوان

        // إخفاء نقطة الإشعار عند التمرير
        let hasScrolled = false;
        window.addEventListener('scroll', function() {
            if (!hasScrolled && window.pageYOffset > 200) {
                hasScrolled = true;
                setTimeout(() => {
                    const notification = document.getElementById('chatNotification');
                    if (notification) {
                        notification.style.animation = 'fadeOut 0.5s ease';
                        setTimeout(() => {
                            notification.style.display = 'none';
                        }, 500);
                    }
                }, 3000);
            }
        });
    </script>
</body>
</html>
